import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'

export default async function handler(req, res) {
  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Check for development auth bypass first to prevent infinite loops
    const isDevelopment = process.env.NODE_ENV === 'development'
    const authBypass = process.env.ENABLE_AUTH_BYPASS === 'true'

    if (isDevelopment && authBypass) {
      console.log('User stats API: Development auth bypass enabled, returning mock data')
      // Return mock stats data to prevent infinite loops during development
      return res.status(200).json({
        totalUsers: 5,
        activeUsers: 3,
        newThisMonth: 1,
        pendingApplications: 2,
        roleBreakdown: {
          dev: 1,
          admin: 1,
          artist: 1,
          braider: 1,
          user: 1
        }
      })
    }

    // Authenticate request using our robust auth module
    const { authorized, error, user, role } = await authenticateAdminRequest(req)

    if (!authorized) {
      console.error('User stats API: Authentication failed:', error?.message || 'Unknown error')
      return res.status(401).json({
        error: 'Unauthorized access',
        message: error?.message || 'Authentication failed'
      })
    }

    console.log('User stats API: Authentication successful. User:', user?.email, 'Role:', role)
    if (!user || !['admin', 'dev'].includes(role)) {
      return res.status(403).json({ error: 'Unauthorized. Only administrators and developers can view user statistics.' })
    }

    // Get admin client to bypass RLS policies
    const adminClient = getAdminClient()
    if (!adminClient) {
      return res.status(500).json({ error: 'Failed to initialize admin client' })
    }

    // Get total users count
    const { count: totalUsers, error: totalUsersError } = await adminClient
      .from('user_profiles')
      .select('*', { count: 'exact', head: true })

    if (totalUsersError) {
      console.error('Error fetching total users:', totalUsersError)
      return res.status(500).json({ error: 'Failed to fetch total users count' })
    }

    // Get active users count
    const { count: activeUsers, error: activeUsersError } = await adminClient
      .from('user_profiles')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    if (activeUsersError) {
      console.error('Error fetching active users:', activeUsersError)
      return res.status(500).json({ error: 'Failed to fetch active users count' })
    }

    // Get new users this month
    const startOfMonth = new Date()
    startOfMonth.setDate(1)
    startOfMonth.setHours(0, 0, 0, 0)

    const { count: newThisMonth, error: newUsersError } = await adminClient
      .from('user_profiles')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startOfMonth.toISOString())

    if (newUsersError) {
      console.error('Error fetching new users this month:', newUsersError)
      return res.status(500).json({ error: 'Failed to fetch new users count' })
    }

    // Get pending applications count
    const { count: pendingApplications, error: applicationsError } = await adminClient
      .from('artist_braider_applications')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending')

    if (applicationsError) {
      console.error('Error fetching pending applications:', applicationsError)
      return res.status(500).json({ error: 'Failed to fetch pending applications count' })
    }

    // Get role breakdown
    const { data: roleData, error: roleError } = await adminClient
      .from('user_roles')
      .select('role')

    if (roleError) {
      console.error('Error fetching role breakdown:', roleError)
      return res.status(500).json({ error: 'Failed to fetch role breakdown' })
    }

    // Count roles
    const roleBreakdown = {
      dev: 0,
      admin: 0,
      artist: 0,
      braider: 0,
      user: 0
    }

    roleData.forEach(user => {
      if (roleBreakdown.hasOwnProperty(user.role)) {
        roleBreakdown[user.role]++
      }
    })

    // Return statistics
    return res.status(200).json({
      totalUsers: totalUsers || 0,
      activeUsers: activeUsers || 0,
      newThisMonth: newThisMonth || 0,
      pendingApplications: pendingApplications || 0,
      roleBreakdown
    })

  } catch (error) {
    console.error('Unexpected error fetching user statistics:', error)
    return res.status(500).json({ error: 'An unexpected error occurred' })
  }
}
