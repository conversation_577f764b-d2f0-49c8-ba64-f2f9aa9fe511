import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export default async function handler(req, res) {
  try {
    // Get the authorization header
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' })
    }

    const token = authHeader.split(' ')[1]

    // Verify the user's session
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' })
    }

    // Get user role
    const { data: userProfile, error: profileError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .single()

    if (profileError || !userProfile) {
      return res.status(403).json({ error: 'User role not found' })
    }

    const userRole = userProfile.role

    // Only DEV users can access this endpoint
    if (userRole !== 'dev') {
      return res.status(403).json({ error: 'Access denied. Developer privileges required.' })
    }

    if (req.method === 'GET') {
      // Get all role permissions
      const { data: rolePermissions, error: permissionsError } = await supabase
        .from('role_permissions')
        .select('*')
        .order('role_name', { ascending: true })
        .order('category', { ascending: true })
        .order('permission_name', { ascending: true })

      if (permissionsError) {
        console.error('Error fetching role permissions:', permissionsError)
        return res.status(500).json({ error: 'Failed to fetch role permissions' })
      }

      // Group permissions by role and category
      const permissionMatrix = {}
      rolePermissions.forEach(permission => {
        if (!permissionMatrix[permission.permission_name]) {
          permissionMatrix[permission.permission_name] = {
            name: permission.permission_name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            category: permission.category,
            description: permission.description
          }
        }
        permissionMatrix[permission.permission_name][permission.role_name] = permission.default_value
      })

      return res.status(200).json({
        success: true,
        permissions: permissionMatrix
      })

    } else if (req.method === 'POST') {
      // Update role permissions
      const { permissions } = req.body

      if (!permissions || typeof permissions !== 'object') {
        return res.status(400).json({ error: 'Invalid permissions data' })
      }

      try {
        // Update permissions in database
        const updates = []
        
        for (const [permissionName, permissionData] of Object.entries(permissions)) {
          for (const [roleName, value] of Object.entries(permissionData)) {
            if (['dev', 'admin', 'artist', 'braider', 'user'].includes(roleName)) {
              updates.push({
                role_name: roleName,
                permission_name: permissionName,
                default_value: value,
                description: permissionData.description || '',
                category: permissionData.category || 'general'
              })
            }
          }
        }

        // Use upsert to update or insert permissions
        const { error: updateError } = await supabase
          .from('role_permissions')
          .upsert(updates, { 
            onConflict: 'role_name,permission_name',
            ignoreDuplicates: false 
          })

        if (updateError) {
          console.error('Error updating role permissions:', updateError)
          return res.status(500).json({ error: 'Failed to update role permissions' })
        }

        // Log the activity
        await supabase
          .from('user_activity_logs')
          .insert({
            user_id: user.id,
            action: 'update_role_permissions',
            details: `Updated role permissions matrix`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          })

        return res.status(200).json({
          success: true,
          message: 'Role permissions updated successfully'
        })

      } catch (error) {
        console.error('Error updating permissions:', error)
        return res.status(500).json({ error: 'Internal server error' })
      }

    } else {
      res.setHeader('Allow', ['GET', 'POST'])
      return res.status(405).json({ error: 'Method not allowed' })
    }

  } catch (error) {
    console.error('API error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
