import { useRouter } from 'next/router'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect, useState, useRef } from 'react'
import { toast } from 'react-toastify'

export default function ProtectedRoute({
  children,
  adminOnly = false,
  devOnly = false,
  staffOnly = false,
  requiredRole = null
}) {
  const {
    user,
    role,
    loading: authLoading,
    hasAdminAccess,
    hasStaffAccess,
    hasFullAccess,
    isDev,
    isAdmin,
    isStaff
  } = useAuth()
  const router = useRouter()
  const [isAuthorized, setIsAuthorized] = useState(false)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(true)
  const [authTimeout, setAuthTimeout] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const timeoutRef = useRef(null)
  const retryTimeoutRef = useRef(null)

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current)
      }
    }
  }, [])

  // Set up authentication timeout mechanism
  useEffect(() => {
    if (authLoading && !authTimeout) {
      // Set a timeout for authentication
      timeoutRef.current = setTimeout(() => {
        console.log('ProtectedRoute: Authentication timeout reached')
        setAuthTimeout(true)

        // Attempt automatic recovery
        if (retryCount < 3) {
          console.log(`ProtectedRoute: Attempting recovery (attempt ${retryCount + 1}/3)`)
          setRetryCount(prev => prev + 1)

          // Force a page refresh after a short delay to trigger re-authentication
          retryTimeoutRef.current = setTimeout(() => {
            console.log('ProtectedRoute: Forcing page refresh for recovery')
            window.location.reload()
          }, 2000)
        } else {
          console.log('ProtectedRoute: Max retry attempts reached, redirecting to login')
          // After max retries, redirect to login
          router.push('/admin/login')
        }
      }, 15000) // 15 second timeout
    }

    // Clear timeout if auth completes
    if (!authLoading && timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
      setAuthTimeout(false)
    }
  }, [authLoading, authTimeout, retryCount, router])

  // Handle authentication and authorization with enhanced redirect loop prevention
  useEffect(() => {
    // Skip if still loading auth state
    if (authLoading) {
      console.log('ProtectedRoute: Auth still loading...')
      return
    }

    const checkAccess = async () => {
      try {
        console.log('ProtectedRoute: Checking access for user:', user?.email, 'role:', role)

        // Check if we're already on a login page to prevent redirect loops
        const isOnLoginPage = router.pathname === '/admin/login' ||
                             router.pathname === '/admin/reset-password' ||
                             router.pathname === '/admin/forgot-password'

        // If no user and not on login page, redirect to login
        if (!user && !isOnLoginPage) {
          console.log('ProtectedRoute: No user found, redirecting to login')

          // Set loading to false before redirecting
          setLoading(false)

          // Prevent multiple simultaneous redirects using component state
          if (router.events) {
            const handleRouteChangeStart = () => {
              sessionStorage.removeItem('protected_route_redirecting')
            }
            router.events.on('routeChangeStart', handleRouteChangeStart)
            return () => router.events.off('routeChangeStart', handleRouteChangeStart)
          }
          sessionStorage.setItem('redirect_after_login', router.asPath)

          // Use replace to prevent back button issues
          router.replace('/admin/login').finally(() => {
            setTimeout(() => {
              sessionStorage.removeItem('protected_route_redirecting')
            }, 1000)
          })
          return
        }

        // If user exists but we're on login page, redirect to intended destination
        if (user && isOnLoginPage) {
          const redirectPath = sessionStorage.getItem('redirect_after_login') || '/admin'
          sessionStorage.removeItem('redirect_after_login')
          console.log('ProtectedRoute: User authenticated on login page, redirecting to:', redirectPath)

          // Set loading to false before redirecting
          setLoading(false)

          router.replace(redirectPath)
          return
        }

        // Enhanced authorization logic with new role system
        // Special case for known admin users (fallback)
        const knownAdmins = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        const isKnownAdmin = knownAdmins.includes(user.email);

        // DEV users have unrestricted access to everything
        if (isDev || isKnownAdmin) {
          console.log('ProtectedRoute: DEV user or known admin - granting unrestricted access')
          setIsAuthorized(true)
          setError(null)
          return
        }

        // Check specific role requirements for non-DEV users
        let authorized = false
        let errorMessage = 'Access denied.'

        if (devOnly) {
          authorized = false // Only actual DEV users can access dev-only sections
          errorMessage = 'Access denied. Developer privileges required.'
        } else if (adminOnly) {
          authorized = hasAdminAccess
          errorMessage = 'Access denied. Admin privileges required.'
        } else if (staffOnly) {
          authorized = hasStaffAccess
          errorMessage = 'Access denied. Staff privileges required.'
        } else if (requiredRole) {
          authorized = role === requiredRole
          errorMessage = `Access denied. ${requiredRole} role required.`
        } else {
          // Default: require at least staff access for admin panel
          authorized = hasStaffAccess
          errorMessage = 'Access denied. Staff privileges required.'
        }

        if (!authorized) {
          console.log('ProtectedRoute: User lacks required privileges', {
            role,
            devOnly,
            adminOnly,
            staffOnly,
            requiredRole
          })
          setError(errorMessage)
          toast.error(errorMessage, {
            autoClose: 5000,
            position: 'top-center'
          })
          setIsAuthorized(false)
        } else {
          // User is authorized
          console.log('ProtectedRoute: User authorized', { role, authorized })
          setIsAuthorized(true)
          setError(null)
        }

        setLoading(false)
      } catch (error) {
        console.error('Access verification failed:', error)
        setError('Authentication failed: ' + (error?.message || 'Unknown error'))
        setLoading(false)
      }
    }

    checkAccess()
  }, [user, role, authLoading, adminOnly, devOnly, staffOnly, requiredRole, router, hasAdminAccess, hasStaffAccess, isDev])

  // Show loading state, error, or children
  if (authLoading || loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen" data-testid="auth-loading">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500 mb-4"></div>
        <div className="text-gray-600 auth-loading">
          {authTimeout ? 'Attempting recovery...' : 'Authenticating...'}
        </div>
        <div className="text-xs text-gray-400 mt-2">
          {authTimeout ? (
            retryCount < 3 ? (
              `Recovery attempt ${retryCount}/3 - Page will refresh automatically`
            ) : (
              'Redirecting to login...'
            )
          ) : (
            'If this takes too long, the page will automatically recover'
          )}
        </div>
        {authTimeout && retryCount > 0 && (
          <div className="text-xs text-orange-500 mt-1">
            Authentication is taking longer than expected. Automatic recovery in progress.
          </div>
        )}
      </div>
    )
  }

  if (!isAuthorized) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <div className="text-red-500 text-center mt-4 px-4 max-w-md">
          <p className="font-semibold">Authorization Error:</p>
          <p>{error || 'Unauthorized access'}</p>
          <button
            onClick={() => router.push('/admin/login')}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Return to Login
          </button>
        </div>
      </div>
    )
  }

  return children
}
