import { useState, useEffect } from 'react'
import styles from '@/styles/admin/user-profiles/ProfileSettings.module.css'

export default function ProfileSettings({ onError, onLoading }) {
  const [users, setUsers] = useState([])
  const [selectedUser, setSelectedUser] = useState(null)
  const [profileSettings, setProfileSettings] = useState({})
  const [dashboardWidgets, setDashboardWidgets] = useState([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const availableWidgets = [
    { id: 'stats_overview', name: 'Statistics Overview', description: 'User and system statistics' },
    { id: 'recent_activity', name: 'Recent Activity', description: 'Latest user activities' },
    { id: 'booking_calendar', name: 'Booking Calendar', description: 'Upcoming appointments' },
    { id: 'commission_summary', name: 'Commission Summary', description: 'Earnings overview' },
    { id: 'customer_list', name: 'Customer List', description: 'Recent customers' },
    { id: 'inventory_alerts', name: 'Inventory Alerts', description: 'Low stock notifications' },
    { id: 'payment_summary', name: 'Payment Summary', description: 'Payment statistics' },
    { id: 'gallery_showcase', name: 'Gallery Showcase', description: 'Featured work' }
  ]

  const profileSettingsTemplate = [
    {
      key: 'profile_visibility',
      name: 'Profile Visibility',
      type: 'select',
      options: ['public', 'admin_only', 'private'],
      description: 'Who can view this user profile'
    },
    {
      key: 'show_commission_rates',
      name: 'Show Commission Rates',
      type: 'boolean',
      description: 'Display commission rates in admin view'
    },
    {
      key: 'show_contact_info',
      name: 'Show Contact Information',
      type: 'boolean',
      description: 'Display contact information to other users'
    },
    {
      key: 'dashboard_theme',
      name: 'Dashboard Theme',
      type: 'select',
      options: ['light', 'dark', 'auto'],
      description: 'Dashboard appearance theme'
    },
    {
      key: 'notification_preferences',
      name: 'Notification Preferences',
      type: 'json',
      description: 'Email and push notification settings'
    },
    {
      key: 'working_hours',
      name: 'Working Hours',
      type: 'json',
      description: 'Default working hours and availability'
    }
  ]

  useEffect(() => {
    loadUsers()
  }, [])

  useEffect(() => {
    if (selectedUser) {
      loadUserSettings(selectedUser.id)
    }
  }, [selectedUser])

  const loadUsers = async () => {
    try {
      setLoading(true)
      onLoading?.(true)

      // Mock user data
      const mockUsers = [
        { id: '1', name: 'Sarah Johnson', email: '<EMAIL>', role: 'artist' },
        { id: '2', name: 'Mike Chen', email: '<EMAIL>', role: 'braider' },
        { id: '3', name: 'Emma Davis', email: '<EMAIL>', role: 'artist' },
        { id: '4', name: 'Admin User', email: '<EMAIL>', role: 'admin' }
      ]

      setUsers(mockUsers)

    } catch (error) {
      console.error('Error loading users:', error)
      onError?.('Failed to load users')
    } finally {
      setLoading(false)
      onLoading?.(false)
    }
  }

  const loadUserSettings = async (userId) => {
    try {
      setLoading(true)

      // Mock settings data
      const mockSettings = {
        profile_visibility: 'admin_only',
        show_commission_rates: true,
        show_contact_info: false,
        dashboard_theme: 'light',
        notification_preferences: {
          email_bookings: true,
          email_payments: true,
          push_notifications: false
        },
        working_hours: {
          monday: { start: '09:00', end: '17:00', available: true },
          tuesday: { start: '09:00', end: '17:00', available: true },
          wednesday: { start: '09:00', end: '17:00', available: true },
          thursday: { start: '09:00', end: '17:00', available: true },
          friday: { start: '09:00', end: '17:00', available: true },
          saturday: { start: '10:00', end: '16:00', available: false },
          sunday: { start: '10:00', end: '16:00', available: false }
        }
      }

      const mockWidgets = [
        { widget_name: 'stats_overview', is_enabled: true, widget_order: 1 },
        { widget_name: 'recent_activity', is_enabled: true, widget_order: 2 },
        { widget_name: 'booking_calendar', is_enabled: true, widget_order: 3 },
        { widget_name: 'commission_summary', is_enabled: false, widget_order: 4 }
      ]

      setProfileSettings(mockSettings)
      setDashboardWidgets(mockWidgets)

    } catch (error) {
      console.error('Error loading user settings:', error)
      onError?.('Failed to load user settings')
    } finally {
      setLoading(false)
    }
  }

  const handleSettingChange = (key, value) => {
    setProfileSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleWidgetToggle = (widgetName) => {
    setDashboardWidgets(prev => 
      prev.map(widget => 
        widget.widget_name === widgetName 
          ? { ...widget, is_enabled: !widget.is_enabled }
          : widget
      )
    )
  }

  const saveSettings = async () => {
    try {
      setSaving(true)

      // Mock API call
      console.log('Saving settings for user:', selectedUser.id)
      console.log('Profile settings:', profileSettings)
      console.log('Dashboard widgets:', dashboardWidgets)

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000))

    } catch (error) {
      console.error('Error saving settings:', error)
      onError?.('Failed to save settings')
    } finally {
      setSaving(false)
    }
  }

  const renderSettingInput = (setting) => {
    const value = profileSettings[setting.key]

    switch (setting.type) {
      case 'boolean':
        return (
          <label className={styles.checkboxLabel}>
            <input
              type="checkbox"
              checked={value || false}
              onChange={(e) => handleSettingChange(setting.key, e.target.checked)}
            />
            <span className={styles.checkboxCustom}></span>
            {setting.name}
          </label>
        )

      case 'select':
        return (
          <div className={styles.selectGroup}>
            <label>{setting.name}</label>
            <select
              value={value || ''}
              onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            >
              <option value="">Select...</option>
              {setting.options.map(option => (
                <option key={option} value={option}>
                  {option.charAt(0).toUpperCase() + option.slice(1)}
                </option>
              ))}
            </select>
          </div>
        )

      case 'json':
        return (
          <div className={styles.jsonGroup}>
            <label>{setting.name}</label>
            <textarea
              value={JSON.stringify(value || {}, null, 2)}
              onChange={(e) => {
                try {
                  const parsed = JSON.parse(e.target.value)
                  handleSettingChange(setting.key, parsed)
                } catch (err) {
                  // Invalid JSON, don't update
                }
              }}
              rows="6"
              className={styles.jsonTextarea}
            />
          </div>
        )

      default:
        return (
          <div className={styles.inputGroup}>
            <label>{setting.name}</label>
            <input
              type="text"
              value={value || ''}
              onChange={(e) => handleSettingChange(setting.key, e.target.value)}
            />
          </div>
        )
    }
  }

  if (loading && !selectedUser) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading profile settings...</p>
      </div>
    )
  }

  return (
    <div className={styles.profileSettings}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h2>Profile Settings</h2>
          <p>Customize user profile visibility and dashboard widgets</p>
        </div>
        <div className={styles.headerActions}>
          {selectedUser && (
            <button 
              className={styles.saveButton}
              onClick={saveSettings}
              disabled={saving}
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </button>
          )}
        </div>
      </div>

      {/* User Selection */}
      <div className={styles.userSelection}>
        <label>Select User</label>
        <select
          value={selectedUser?.id || ''}
          onChange={(e) => {
            const user = users.find(u => u.id === e.target.value)
            setSelectedUser(user)
          }}
        >
          <option value="">Choose a user...</option>
          {users.map(user => (
            <option key={user.id} value={user.id}>
              {user.name} ({user.role}) - {user.email}
            </option>
          ))}
        </select>
      </div>

      {selectedUser && (
        <div className={styles.settingsContainer}>
          {/* Profile Settings */}
          <div className={styles.settingsSection}>
            <h3>Profile Settings</h3>
            <div className={styles.settingsGrid}>
              {profileSettingsTemplate.map(setting => (
                <div key={setting.key} className={styles.settingItem}>
                  {renderSettingInput(setting)}
                  <p className={styles.settingDescription}>
                    {setting.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Dashboard Widgets */}
          <div className={styles.settingsSection}>
            <h3>Dashboard Widgets</h3>
            <p>Configure which widgets are visible on the user's dashboard</p>
            <div className={styles.widgetsGrid}>
              {availableWidgets.map(widget => {
                const userWidget = dashboardWidgets.find(w => w.widget_name === widget.id)
                const isEnabled = userWidget?.is_enabled || false

                return (
                  <div key={widget.id} className={styles.widgetCard}>
                    <div className={styles.widgetHeader}>
                      <label className={styles.widgetToggle}>
                        <input
                          type="checkbox"
                          checked={isEnabled}
                          onChange={() => handleWidgetToggle(widget.id)}
                        />
                        <span className={styles.toggleSlider}></span>
                      </label>
                      <h4>{widget.name}</h4>
                    </div>
                    <p>{widget.description}</p>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      )}

      {!selectedUser && (
        <div className={styles.emptyState}>
          <div className={styles.emptyIcon}>👤</div>
          <h3>Select a User</h3>
          <p>Choose a user from the dropdown to configure their profile settings</p>
        </div>
      )}
    </div>
  )
}
