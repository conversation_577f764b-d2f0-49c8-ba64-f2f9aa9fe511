import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export default async function handler(req, res) {
  try {
    // Get the authorization header
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'Missing or invalid authorization header' })
    }

    const token = authHeader.split(' ')[1]

    // Verify the user's session
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid authentication token' })
    }

    // Get user role
    const { data: userProfile, error: profileError } = await supabase
      .from('user_roles')
      .select('role')
      .eq('user_id', user.id)
      .single()

    if (profileError || !userProfile) {
      return res.status(403).json({ error: 'User role not found' })
    }

    const userRole = userProfile.role

    // Only DEV users can access this endpoint for managing other users
    // Users can access their own settings
    const { user_id } = req.query
    const isOwnSettings = user_id === user.id
    const canManageSettings = userRole === 'dev' || isOwnSettings

    if (!canManageSettings) {
      return res.status(403).json({ error: 'Access denied. Developer privileges required.' })
    }

    if (req.method === 'GET') {
      const targetUserId = user_id || user.id

      // Get user profile settings
      const { data: profileSettings, error: settingsError } = await supabase
        .from('user_profile_settings')
        .select('*')
        .eq('user_id', targetUserId)

      if (settingsError) {
        console.error('Error fetching profile settings:', settingsError)
        return res.status(500).json({ error: 'Failed to fetch profile settings' })
      }

      // Get user dashboard widgets
      const { data: dashboardWidgets, error: widgetsError } = await supabase
        .from('user_dashboard_widgets')
        .select('*')
        .eq('user_id', targetUserId)
        .order('widget_order', { ascending: true })

      if (widgetsError) {
        console.error('Error fetching dashboard widgets:', widgetsError)
        return res.status(500).json({ error: 'Failed to fetch dashboard widgets' })
      }

      // Format settings as key-value pairs
      const settings = {}
      profileSettings.forEach(setting => {
        settings[setting.setting_name] = setting.setting_value
      })

      return res.status(200).json({
        success: true,
        settings,
        dashboardWidgets
      })

    } else if (req.method === 'POST') {
      // Update user profile settings and dashboard widgets
      const targetUserId = user_id || user.id
      const { settings, dashboardWidgets } = req.body

      if (!settings && !dashboardWidgets) {
        return res.status(400).json({ error: 'No settings or widgets provided' })
      }

      try {
        // Update profile settings
        if (settings) {
          const settingsUpdates = Object.entries(settings).map(([key, value]) => ({
            user_id: targetUserId,
            setting_name: key,
            setting_value: value,
            setting_type: typeof value === 'object' ? 'json' : typeof value,
            is_visible_to_admin: true,
            is_editable_by_user: !['profile_visibility', 'show_commission_rates'].includes(key)
          }))

          const { error: settingsError } = await supabase
            .from('user_profile_settings')
            .upsert(settingsUpdates, { 
              onConflict: 'user_id,setting_name',
              ignoreDuplicates: false 
            })

          if (settingsError) {
            console.error('Error updating profile settings:', settingsError)
            return res.status(500).json({ error: 'Failed to update profile settings' })
          }
        }

        // Update dashboard widgets
        if (dashboardWidgets && Array.isArray(dashboardWidgets)) {
          // First, delete existing widgets for this user
          await supabase
            .from('user_dashboard_widgets')
            .delete()
            .eq('user_id', targetUserId)

          // Then insert new widget configurations
          const widgetUpdates = dashboardWidgets.map((widget, index) => ({
            user_id: targetUserId,
            widget_name: widget.widget_name,
            is_enabled: widget.is_enabled,
            widget_order: widget.widget_order || index,
            widget_config: widget.widget_config || {}
          }))

          const { error: widgetsError } = await supabase
            .from('user_dashboard_widgets')
            .insert(widgetUpdates)

          if (widgetsError) {
            console.error('Error updating dashboard widgets:', widgetsError)
            return res.status(500).json({ error: 'Failed to update dashboard widgets' })
          }
        }

        // Log the activity
        await supabase
          .from('user_activity_logs')
          .insert({
            user_id: user.id,
            action: 'update_profile_settings',
            details: `Updated profile settings for user ${targetUserId}`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          })

        return res.status(200).json({
          success: true,
          message: 'Profile settings updated successfully'
        })

      } catch (error) {
        console.error('Error updating profile settings:', error)
        return res.status(500).json({ error: 'Internal server error' })
      }

    } else if (req.method === 'DELETE') {
      // Reset user settings to defaults
      const targetUserId = user_id || user.id

      try {
        // Delete all custom settings
        await supabase
          .from('user_profile_settings')
          .delete()
          .eq('user_id', targetUserId)

        // Reset dashboard widgets to defaults
        await supabase
          .from('user_dashboard_widgets')
          .delete()
          .eq('user_id', targetUserId)

        // Insert default dashboard widgets
        const defaultWidgets = [
          { widget_name: 'stats_overview', is_enabled: true, widget_order: 1 },
          { widget_name: 'recent_activity', is_enabled: true, widget_order: 2 },
          { widget_name: 'booking_calendar', is_enabled: true, widget_order: 3 }
        ]

        const widgetInserts = defaultWidgets.map(widget => ({
          user_id: targetUserId,
          ...widget,
          widget_config: {}
        }))

        await supabase
          .from('user_dashboard_widgets')
          .insert(widgetInserts)

        // Log the activity
        await supabase
          .from('user_activity_logs')
          .insert({
            user_id: user.id,
            action: 'reset_profile_settings',
            details: `Reset profile settings to defaults for user ${targetUserId}`,
            ip_address: req.headers['x-forwarded-for'] || req.connection.remoteAddress,
            user_agent: req.headers['user-agent']
          })

        return res.status(200).json({
          success: true,
          message: 'Profile settings reset to defaults'
        })

      } catch (error) {
        console.error('Error resetting profile settings:', error)
        return res.status(500).json({ error: 'Internal server error' })
      }

    } else {
      res.setHeader('Allow', ['GET', 'POST', 'DELETE'])
      return res.status(405).json({ error: 'Method not allowed' })
    }

  } catch (error) {
    console.error('API error:', error)
    return res.status(500).json({ error: 'Internal server error' })
  }
}
