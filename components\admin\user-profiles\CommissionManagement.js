import { useState, useEffect } from 'react'
import styles from '@/styles/admin/user-profiles/CommissionManagement.module.css'

export default function CommissionManagement({ onError, onLoading }) {
  const [commissionRates, setCommissionRates] = useState([])
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [editingRate, setEditingRate] = useState(null)
  const [showAddForm, setShowAddForm] = useState(false)

  const [newRate, setNewRate] = useState({
    user_id: '',
    service_category: '',
    commission_percentage: '',
    base_rate: '',
    bonus_rate: '',
    effective_from: new Date().toISOString().split('T')[0],
    notes: ''
  })

  const serviceCategories = [
    'Hair Braiding',
    'Makeup Application',
    'Eyebrow Styling',
    'Eyelash Extensions',
    'Nail Services',
    'Facial Treatments',
    'Special Events',
    'Bridal Services',
    'Photography Sessions',
    'General Services'
  ]

  useEffect(() => {
    loadCommissionData()
  }, [])

  const loadCommissionData = async () => {
    try {
      setLoading(true)
      onLoading?.(true)

      // Mock data for demonstration
      const mockUsers = [
        { id: '1', name: 'Sarah Johnson', email: '<EMAIL>', role: 'artist' },
        { id: '2', name: 'Mike Chen', email: '<EMAIL>', role: 'braider' },
        { id: '3', name: 'Emma Davis', email: '<EMAIL>', role: 'artist' },
        { id: '4', name: 'Alex Rodriguez', email: '<EMAIL>', role: 'braider' }
      ]

      const mockRates = [
        {
          id: '1',
          user_id: '1',
          user_name: 'Sarah Johnson',
          service_category: 'Makeup Application',
          commission_percentage: 60,
          base_rate: 80,
          bonus_rate: 10,
          effective_from: '2024-01-01',
          effective_until: null,
          is_active: true,
          notes: 'Standard makeup artist rate'
        },
        {
          id: '2',
          user_id: '2',
          user_name: 'Mike Chen',
          service_category: 'Hair Braiding',
          commission_percentage: 65,
          base_rate: 120,
          bonus_rate: 15,
          effective_from: '2024-01-01',
          effective_until: null,
          is_active: true,
          notes: 'Experienced braider premium rate'
        }
      ]

      setUsers(mockUsers.filter(user => ['artist', 'braider'].includes(user.role)))
      setCommissionRates(mockRates)

    } catch (error) {
      console.error('Error loading commission data:', error)
      onError?.('Failed to load commission data')
    } finally {
      setLoading(false)
      onLoading?.(false)
    }
  }

  const handleAddRate = async () => {
    try {
      setSaving(true)

      // Validate form
      if (!newRate.user_id || !newRate.service_category || !newRate.commission_percentage) {
        onError?.('Please fill in all required fields')
        return
      }

      // Mock API call
      const newRateData = {
        id: Date.now().toString(),
        ...newRate,
        user_name: users.find(u => u.id === newRate.user_id)?.name || 'Unknown',
        is_active: true
      }

      setCommissionRates(prev => [...prev, newRateData])
      setNewRate({
        user_id: '',
        service_category: '',
        commission_percentage: '',
        base_rate: '',
        bonus_rate: '',
        effective_from: new Date().toISOString().split('T')[0],
        notes: ''
      })
      setShowAddForm(false)

    } catch (error) {
      console.error('Error adding commission rate:', error)
      onError?.('Failed to add commission rate')
    } finally {
      setSaving(false)
    }
  }

  const handleUpdateRate = async (rateId, updates) => {
    try {
      setSaving(true)

      setCommissionRates(prev => 
        prev.map(rate => 
          rate.id === rateId ? { ...rate, ...updates } : rate
        )
      )

      setEditingRate(null)

    } catch (error) {
      console.error('Error updating commission rate:', error)
      onError?.('Failed to update commission rate')
    } finally {
      setSaving(false)
    }
  }

  const handleDeleteRate = async (rateId) => {
    try {
      setSaving(true)

      setCommissionRates(prev => prev.filter(rate => rate.id !== rateId))

    } catch (error) {
      console.error('Error deleting commission rate:', error)
      onError?.('Failed to delete commission rate')
    } finally {
      setSaving(false)
    }
  }

  const calculateEarnings = (rate) => {
    const baseEarnings = rate.base_rate || 0
    const commissionEarnings = baseEarnings * (rate.commission_percentage / 100)
    const bonusEarnings = rate.bonus_rate || 0
    return {
      base: baseEarnings,
      commission: commissionEarnings,
      bonus: bonusEarnings,
      total: commissionEarnings + bonusEarnings
    }
  }

  if (loading) {
    return (
      <div className={styles.loading}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading commission data...</p>
      </div>
    )
  }

  return (
    <div className={styles.commissionManagement}>
      <div className={styles.header}>
        <div className={styles.headerContent}>
          <h2>Commission Management</h2>
          <p>Manage commission rates for artists and braiders</p>
        </div>
        <div className={styles.headerActions}>
          <button 
            className={styles.addButton}
            onClick={() => setShowAddForm(true)}
            disabled={saving}
          >
            + Add Commission Rate
          </button>
        </div>
      </div>

      {/* Add New Rate Form */}
      {showAddForm && (
        <div className={styles.addForm}>
          <div className={styles.formHeader}>
            <h3>Add New Commission Rate</h3>
            <button 
              className={styles.closeButton}
              onClick={() => setShowAddForm(false)}
            >
              ✕
            </button>
          </div>
          
          <div className={styles.formGrid}>
            <div className={styles.formGroup}>
              <label>User *</label>
              <select
                value={newRate.user_id}
                onChange={(e) => setNewRate(prev => ({ ...prev, user_id: e.target.value }))}
              >
                <option value="">Select User</option>
                {users.map(user => (
                  <option key={user.id} value={user.id}>
                    {user.name} ({user.role})
                  </option>
                ))}
              </select>
            </div>

            <div className={styles.formGroup}>
              <label>Service Category *</label>
              <select
                value={newRate.service_category}
                onChange={(e) => setNewRate(prev => ({ ...prev, service_category: e.target.value }))}
              >
                <option value="">Select Category</option>
                {serviceCategories.map(category => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            <div className={styles.formGroup}>
              <label>Commission % *</label>
              <input
                type="number"
                min="0"
                max="100"
                value={newRate.commission_percentage}
                onChange={(e) => setNewRate(prev => ({ ...prev, commission_percentage: e.target.value }))}
                placeholder="60"
              />
            </div>

            <div className={styles.formGroup}>
              <label>Base Rate ($)</label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={newRate.base_rate}
                onChange={(e) => setNewRate(prev => ({ ...prev, base_rate: e.target.value }))}
                placeholder="100.00"
              />
            </div>

            <div className={styles.formGroup}>
              <label>Bonus Rate ($)</label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={newRate.bonus_rate}
                onChange={(e) => setNewRate(prev => ({ ...prev, bonus_rate: e.target.value }))}
                placeholder="10.00"
              />
            </div>

            <div className={styles.formGroup}>
              <label>Effective From</label>
              <input
                type="date"
                value={newRate.effective_from}
                onChange={(e) => setNewRate(prev => ({ ...prev, effective_from: e.target.value }))}
              />
            </div>

            <div className={styles.formGroup} style={{ gridColumn: '1 / -1' }}>
              <label>Notes</label>
              <textarea
                value={newRate.notes}
                onChange={(e) => setNewRate(prev => ({ ...prev, notes: e.target.value }))}
                placeholder="Additional notes about this commission rate..."
                rows="3"
              />
            </div>
          </div>

          <div className={styles.formActions}>
            <button 
              className={styles.cancelButton}
              onClick={() => setShowAddForm(false)}
              disabled={saving}
            >
              Cancel
            </button>
            <button 
              className={styles.saveButton}
              onClick={handleAddRate}
              disabled={saving}
            >
              {saving ? 'Adding...' : 'Add Rate'}
            </button>
          </div>
        </div>
      )}

      {/* Commission Rates Table */}
      <div className={styles.ratesTable}>
        <div className={styles.tableHeader}>
          <div>User</div>
          <div>Service Category</div>
          <div>Commission %</div>
          <div>Base Rate</div>
          <div>Estimated Earnings</div>
          <div>Status</div>
          <div>Actions</div>
        </div>

        {commissionRates.length === 0 ? (
          <div className={styles.emptyState}>
            <div className={styles.emptyIcon}>💰</div>
            <h3>No Commission Rates</h3>
            <p>Add commission rates for artists and braiders</p>
          </div>
        ) : (
          commissionRates.map(rate => {
            const earnings = calculateEarnings(rate)
            return (
              <div key={rate.id} className={styles.tableRow}>
                <div className={styles.userCell}>
                  <div className={styles.userName}>{rate.user_name}</div>
                  <div className={styles.userCategory}>{rate.service_category}</div>
                </div>
                <div>{rate.service_category}</div>
                <div className={styles.percentageCell}>
                  {rate.commission_percentage}%
                </div>
                <div className={styles.rateCell}>
                  ${rate.base_rate || 0}
                </div>
                <div className={styles.earningsCell}>
                  <div className={styles.earningsTotal}>
                    ${earnings.total.toFixed(2)}
                  </div>
                  <div className={styles.earningsBreakdown}>
                    Commission: ${earnings.commission.toFixed(2)}
                    {earnings.bonus > 0 && ` + Bonus: $${earnings.bonus.toFixed(2)}`}
                  </div>
                </div>
                <div>
                  <span className={`${styles.statusBadge} ${rate.is_active ? styles.active : styles.inactive}`}>
                    {rate.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
                <div className={styles.actionsCell}>
                  <button 
                    className={styles.editButton}
                    onClick={() => setEditingRate(rate.id)}
                    disabled={saving}
                  >
                    Edit
                  </button>
                  <button 
                    className={styles.deleteButton}
                    onClick={() => handleDeleteRate(rate.id)}
                    disabled={saving}
                  >
                    Delete
                  </button>
                </div>
              </div>
            )
          })
        )}
      </div>
    </div>
  )
}
